package com.kikitrade.activity.service.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @CreateTime 2022-11-21 13:54
 */
@Configuration
@ConfigurationProperties(prefix = "kactivity")
@Data
public class KactivityModuleProperties {

    @NestedConfigurationProperty
    private LuckFortuneProperties luck;
    @NestedConfigurationProperty
    private RewardProperties reward;
    @NestedConfigurationProperty
    private NFTProperties nft;
    @NestedConfigurationProperty
    private RankingProperties rank;
    @NestedConfigurationProperty
    private TaskProperties task;
    @NestedConfigurationProperty
    private QuestionProperties question;
    @NestedConfigurationProperty
    private EarnallianceProperties earnalliance;
}
