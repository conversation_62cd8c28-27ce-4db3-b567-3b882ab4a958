package com.kikitrade.activity.api;

import com.kikitrade.activity.api.model.ActivityTaskDTO;
import com.kikitrade.activity.api.model.TaskConfigVO;
import com.kikitrade.activity.api.model.CheckInStatusDTO;
import com.kikitrade.activity.api.model.request.TaskListRequest;
import com.kikitrade.activity.api.model.request.Token;
import com.kikitrade.activity.api.model.response.TaskCodeDetailResponse;
import com.kikitrade.activity.api.model.response.TaskCompletedResult;
import com.kikitrade.activity.api.model.response.TaskDetailResponse;
import com.kikitrade.activity.api.model.response.TaskListResponse;
import com.kikitrade.activity.api.model.response.TaskPopResponse;
import com.kikitrade.activity.api.model.response.TaskProgressResponse;
import com.kikitrade.activity.api.model.response.SocialAuthUrlResponse;
import com.kikitrade.activity.api.model.response.VerifyResponse;
import com.kikitrade.activity.api.model.response.*;
import com.kikitrade.activity.model.constant.ActivityConstant;
import com.kikitrade.activity.model.domain.Award;
import com.kikitrade.activity.model.exception.ActivityException;
import com.kikitrade.activity.model.response.ActivityResponse;

import java.util.List;

public interface RemoteTaskService {

    /**
     * 任务列表
     * @param request
     * @return
     */
    List<TaskListResponse> taskList(TaskListRequest request);

    /**
     * 任务详情
     * @param taskId
     * @return
     */
    TaskDetailResponse getTask(String taskId, String customerId);

    /**
     * 根据 code 查询任务详情
     * @param taskCode
     * @param customerId
     * @return
     */
    TaskCodeDetailResponse getTaskByCode(String saasId, String taskCode, String customerId);

    /**
     * 根据 code 查询应得奖励
     * @param saasId
     * @param taskCode
     * @param vipLevelEnum
     * @return
     */
    List<Award> getTaskRewardByCode(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum);

    List<Award> listTaskRewards(String saasId, String taskCode, ActivityConstant.VipLevelEnum vipLevelEnum);

    /**
     * 获取任务进度
     * @param saasId
     * @param taskId
     * @param customerId
     * @return
     */
    TaskProgressResponse getTaskProgress(String saasId, String taskId, String customerId, String type);


    /**
     * 查询早鸟弹窗
     * @param saasId
     * @param customerId
     * @return
     */
    TaskPopResponse getTaskEarlyBirdPop(String saasId, String customerId);

    /**
     * 发布任务
     * @param taskConfigVO
     * @param isGray 是否灰度发布
     * @return
     */
//    TaskManageResponse createOrUpdateTaskConfig(TaskConfigVO taskConfigVO, Boolean isGray);


    /**
     * 校验任务是否满足任务前置校验
     * @param accessToken
     * @param taskId
     * @return
     */
    VerifyResponse verify(Token accessToken, String taskId, String saasId, String ext) throws ActivityException;

    /**
     * 服务端校验指定任务
     * @param saasId
     * @param cid
     * @param scene
     * @param ext
     * @return
     * @throws ActivityException
     */
    VerifyResponse serverVerify(String saasId, String uid, String scene, String ext) throws ActivityException;

    /**
     * 获取任务状态
     * @param saasId
     * @param taskId
     * @param customerId
     * @return
     */
    TaskCompletedResult getTaskStatus(String saasId, String taskId, String customerId);

    /**
     * 获取任务状态
     * @param saasId
     * @param taskId
     * @param customerId
     * @return
     */
    TaskCompletedResult getTaskStatusByCode(String saasId, String taskCode, String customerId);

    /**
     * 做任务
     * @param activityTaskDTO
     * @return
     */
    ActivityResponse<List<Award>> task(ActivityTaskDTO activityTaskDTO);

    String connect(String saasId, String platform, String accessToken, String refreshToken);

    List<String> getTwitterKols(String taskId);

    CheckInStatusDTO checkInStatus(String saasId, String customerId);

    /**
     * 获取任务相关的社媒平台授权URL列表
     * @param saasId SaaS ID
     * @return 社媒平台授权URL列表
     */
    List<SocialAuthUrlResponse> getTasksAuthUrls(String saasId);

}
