package co.evg.scaffold.innerEvent.dal.model;

import co.evg.scaffold.innerEvent.configuration.InnerEventProperties;
import com.kikitrade.framework.ots.annotations.Column;
import com.kikitrade.framework.ots.annotations.PartitionKey;
import com.kikitrade.framework.ots.annotations.Table;
import com.kikitrade.framework.ots.annotations.Transient;
import jakarta.annotation.Resource;
import lombok.Data;
import org.checkerframework.checker.units.qual.C;
import org.nutz.dao.entity.annotation.Comment;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: joseph
 * @date: 2024/8/26 15:31
 */
@Data
@Table(name = "#{@innerEventProperties.innerTable}")
public class InnerEventDO implements Serializable {

    /**
     * 事件ID
     */
    @PartitionKey(name = "id")
    private String id;
    /**
     * 事件名称
     */
    @Column(name = "name")
    private String name;
    /**
     * 事件主体内容 - jsonString
     */
    @Column(name = "body")
    private String body;

    /**
     * 非必须 - 全局事件幂等性唯一id
     * eg. orderId->用户入金事件
     */
    @Column(name = "uid")
    private String uid;
    /**
     * 事件发生时间
     */
    @Column(name = "time", type = Column.Type.INTEGER)
    private Long time;

    @Column(name = "status")
    private String status;

    @Column(name = "created", type = Column.Type.INTEGER)
    private Date created;

    @Column(name = "modified", type = Column.Type.INTEGER)
    private Date modified;

    /**
     * MQ 重试次数
     */
    @Transient
    private int reConsumeTimes;


}
