// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: PaymentTermManaFacade.proto

package com.kikitrade.kcustomer.facade;

/**
 * <pre>
 * 查询QuickPaymentTermList请求的响应结果
 * </pre>
 *
 * Protobuf type {@code com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse}
 */
public final class QueryPaymentTermListResponse extends
    com.google.protobuf.GeneratedMessageV3 implements
    // @@protoc_insertion_point(message_implements:com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse)
    QueryPaymentTermListResponseOrBuilder {
private static final long serialVersionUID = 0L;
  // Use QueryPaymentTermListResponse.newBuilder() to construct.
  private QueryPaymentTermListResponse(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
    super(builder);
  }
  private QueryPaymentTermListResponse() {
    message_ = "";
    paymentWayInfo_ = java.util.Collections.emptyList();
  }

  @java.lang.Override
  @SuppressWarnings({"unused"})
  protected java.lang.Object newInstance(
      UnusedPrivateParameter unused) {
    return new QueryPaymentTermListResponse();
  }

  @java.lang.Override
  public final com.google.protobuf.UnknownFieldSet
  getUnknownFields() {
    return this.unknownFields;
  }
  private QueryPaymentTermListResponse(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    this();
    if (extensionRegistry == null) {
      throw new java.lang.NullPointerException();
    }
    int mutable_bitField0_ = 0;
    com.google.protobuf.UnknownFieldSet.Builder unknownFields =
        com.google.protobuf.UnknownFieldSet.newBuilder();
    try {
      boolean done = false;
      while (!done) {
        int tag = input.readTag();
        switch (tag) {
          case 0:
            done = true;
            break;
          case 8: {

            success_ = input.readBool();
            break;
          }
          case 18: {
            java.lang.String s = input.readStringRequireUtf8();

            message_ = s;
            break;
          }
          case 26: {
            if (!((mutable_bitField0_ & 0x00000001) != 0)) {
              paymentWayInfo_ = new java.util.ArrayList<com.kikitrade.kcustomer.facade.PaymentTermInfo>();
              mutable_bitField0_ |= 0x00000001;
            }
            paymentWayInfo_.add(
                input.readMessage(com.kikitrade.kcustomer.facade.PaymentTermInfo.parser(), extensionRegistry));
            break;
          }
          default: {
            if (!parseUnknownField(
                input, unknownFields, extensionRegistry, tag)) {
              done = true;
            }
            break;
          }
        }
      }
    } catch (com.google.protobuf.InvalidProtocolBufferException e) {
      throw e.setUnfinishedMessage(this);
    } catch (com.google.protobuf.UninitializedMessageException e) {
      throw e.asInvalidProtocolBufferException().setUnfinishedMessage(this);
    } catch (java.io.IOException e) {
      throw new com.google.protobuf.InvalidProtocolBufferException(
          e).setUnfinishedMessage(this);
    } finally {
      if (((mutable_bitField0_ & 0x00000001) != 0)) {
        paymentWayInfo_ = java.util.Collections.unmodifiableList(paymentWayInfo_);
      }
      this.unknownFields = unknownFields.build();
      makeExtensionsImmutable();
    }
  }
  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.kikitrade.kcustomer.facade.PaymentTermManaFacade.internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.kikitrade.kcustomer.facade.PaymentTermManaFacade.internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.class, com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.Builder.class);
  }

  public static final int SUCCESS_FIELD_NUMBER = 1;
  private boolean success_;
  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  @java.lang.Override
  public boolean getSuccess() {
    return success_;
  }

  public static final int MESSAGE_FIELD_NUMBER = 2;
  private volatile java.lang.Object message_;
  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  @java.lang.Override
  public java.lang.String getMessage() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      message_ = s;
      return s;
    }
  }
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getMessageBytes() {
    java.lang.Object ref = message_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      message_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int PAYMENTWAYINFO_FIELD_NUMBER = 3;
  private java.util.List<com.kikitrade.kcustomer.facade.PaymentTermInfo> paymentWayInfo_;
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
   */
  @java.lang.Override
  public java.util.List<com.kikitrade.kcustomer.facade.PaymentTermInfo> getPaymentWayInfoList() {
    return paymentWayInfo_;
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder> 
      getPaymentWayInfoOrBuilderList() {
    return paymentWayInfo_;
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
   */
  @java.lang.Override
  public int getPaymentWayInfoCount() {
    return paymentWayInfo_.size();
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.kcustomer.facade.PaymentTermInfo getPaymentWayInfo(int index) {
    return paymentWayInfo_.get(index);
  }
  /**
   * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
   */
  @java.lang.Override
  public com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder getPaymentWayInfoOrBuilder(
      int index) {
    return paymentWayInfo_.get(index);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (success_ != false) {
      output.writeBool(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      com.google.protobuf.GeneratedMessageV3.writeString(output, 2, message_);
    }
    for (int i = 0; i < paymentWayInfo_.size(); i++) {
      output.writeMessage(3, paymentWayInfo_.get(i));
    }
    unknownFields.writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (success_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(1, success_);
    }
    if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(message_)) {
      size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, message_);
    }
    for (int i = 0; i < paymentWayInfo_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, paymentWayInfo_.get(i));
    }
    size += unknownFields.getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse)) {
      return super.equals(obj);
    }
    com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse other = (com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse) obj;

    if (getSuccess()
        != other.getSuccess()) return false;
    if (!getMessage()
        .equals(other.getMessage())) return false;
    if (!getPaymentWayInfoList()
        .equals(other.getPaymentWayInfoList())) return false;
    if (!unknownFields.equals(other.unknownFields)) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getSuccess());
    hash = (37 * hash) + MESSAGE_FIELD_NUMBER;
    hash = (53 * hash) + getMessage().hashCode();
    if (getPaymentWayInfoCount() > 0) {
      hash = (37 * hash) + PAYMENTWAYINFO_FIELD_NUMBER;
      hash = (53 * hash) + getPaymentWayInfoList().hashCode();
    }
    hash = (29 * hash) + unknownFields.hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input);
  }
  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessageV3
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 查询QuickPaymentTermList请求的响应结果
   * </pre>
   *
   * Protobuf type {@code com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse)
      com.kikitrade.kcustomer.facade.QueryPaymentTermListResponseOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.kikitrade.kcustomer.facade.PaymentTermManaFacade.internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.kikitrade.kcustomer.facade.PaymentTermManaFacade.internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.class, com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.Builder.class);
    }

    // Construct using com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessageV3
              .alwaysUseFieldBuilders) {
        getPaymentWayInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      success_ = false;

      message_ = "";

      if (paymentWayInfoBuilder_ == null) {
        paymentWayInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
      } else {
        paymentWayInfoBuilder_.clear();
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.kikitrade.kcustomer.facade.PaymentTermManaFacade.internal_static_com_kikitrade_kcustomer_facade_QueryPaymentTermListResponse_descriptor;
    }

    @java.lang.Override
    public com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse getDefaultInstanceForType() {
      return com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.getDefaultInstance();
    }

    @java.lang.Override
    public com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse build() {
      com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse buildPartial() {
      com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse result = new com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse(this);
      int from_bitField0_ = bitField0_;
      result.success_ = success_;
      result.message_ = message_;
      if (paymentWayInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0)) {
          paymentWayInfo_ = java.util.Collections.unmodifiableList(paymentWayInfo_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.paymentWayInfo_ = paymentWayInfo_;
      } else {
        result.paymentWayInfo_ = paymentWayInfoBuilder_.build();
      }
      onBuilt();
      return result;
    }

    @java.lang.Override
    public Builder clone() {
      return super.clone();
    }
    @java.lang.Override
    public Builder setField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.setField(field, value);
    }
    @java.lang.Override
    public Builder clearField(
        com.google.protobuf.Descriptors.FieldDescriptor field) {
      return super.clearField(field);
    }
    @java.lang.Override
    public Builder clearOneof(
        com.google.protobuf.Descriptors.OneofDescriptor oneof) {
      return super.clearOneof(oneof);
    }
    @java.lang.Override
    public Builder setRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        int index, java.lang.Object value) {
      return super.setRepeatedField(field, index, value);
    }
    @java.lang.Override
    public Builder addRepeatedField(
        com.google.protobuf.Descriptors.FieldDescriptor field,
        java.lang.Object value) {
      return super.addRepeatedField(field, value);
    }
    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse) {
        return mergeFrom((com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse other) {
      if (other == com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse.getDefaultInstance()) return this;
      if (other.getSuccess() != false) {
        setSuccess(other.getSuccess());
      }
      if (!other.getMessage().isEmpty()) {
        message_ = other.message_;
        onChanged();
      }
      if (paymentWayInfoBuilder_ == null) {
        if (!other.paymentWayInfo_.isEmpty()) {
          if (paymentWayInfo_.isEmpty()) {
            paymentWayInfo_ = other.paymentWayInfo_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePaymentWayInfoIsMutable();
            paymentWayInfo_.addAll(other.paymentWayInfo_);
          }
          onChanged();
        }
      } else {
        if (!other.paymentWayInfo_.isEmpty()) {
          if (paymentWayInfoBuilder_.isEmpty()) {
            paymentWayInfoBuilder_.dispose();
            paymentWayInfoBuilder_ = null;
            paymentWayInfo_ = other.paymentWayInfo_;
            bitField0_ = (bitField0_ & ~0x00000001);
            paymentWayInfoBuilder_ = 
              com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                 getPaymentWayInfoFieldBuilder() : null;
          } else {
            paymentWayInfoBuilder_.addAllMessages(other.paymentWayInfo_);
          }
        }
      }
      this.mergeUnknownFields(other.unknownFields);
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse parsedMessage = null;
      try {
        parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        parsedMessage = (com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse) e.getUnfinishedMessage();
        throw e.unwrapIOException();
      } finally {
        if (parsedMessage != null) {
          mergeFrom(parsedMessage);
        }
      }
      return this;
    }
    private int bitField0_;

    private boolean success_ ;
    /**
     * <code>bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }
    /**
     * <code>bool success = 1;</code>
     * @param value The success to set.
     * @return This builder for chaining.
     */
    public Builder setSuccess(boolean value) {
      
      success_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>bool success = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSuccess() {
      
      success_ = false;
      onChanged();
      return this;
    }

    private java.lang.Object message_ = "";
    /**
     * <code>string message = 2;</code>
     * @return The message.
     */
    public java.lang.String getMessage() {
      java.lang.Object ref = message_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        message_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @return The bytes for message.
     */
    public com.google.protobuf.ByteString
        getMessageBytes() {
      java.lang.Object ref = message_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        message_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string message = 2;</code>
     * @param value The message to set.
     * @return This builder for chaining.
     */
    public Builder setMessage(
        java.lang.String value) {
      if (value == null) {
    throw new NullPointerException();
  }
  
      message_ = value;
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearMessage() {
      
      message_ = getDefaultInstance().getMessage();
      onChanged();
      return this;
    }
    /**
     * <code>string message = 2;</code>
     * @param value The bytes for message to set.
     * @return This builder for chaining.
     */
    public Builder setMessageBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) {
    throw new NullPointerException();
  }
  checkByteStringIsUtf8(value);
      
      message_ = value;
      onChanged();
      return this;
    }

    private java.util.List<com.kikitrade.kcustomer.facade.PaymentTermInfo> paymentWayInfo_ =
      java.util.Collections.emptyList();
    private void ensurePaymentWayInfoIsMutable() {
      if (!((bitField0_ & 0x00000001) != 0)) {
        paymentWayInfo_ = new java.util.ArrayList<com.kikitrade.kcustomer.facade.PaymentTermInfo>(paymentWayInfo_);
        bitField0_ |= 0x00000001;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.kcustomer.facade.PaymentTermInfo, com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder, com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder> paymentWayInfoBuilder_;

    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public java.util.List<com.kikitrade.kcustomer.facade.PaymentTermInfo> getPaymentWayInfoList() {
      if (paymentWayInfoBuilder_ == null) {
        return java.util.Collections.unmodifiableList(paymentWayInfo_);
      } else {
        return paymentWayInfoBuilder_.getMessageList();
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public int getPaymentWayInfoCount() {
      if (paymentWayInfoBuilder_ == null) {
        return paymentWayInfo_.size();
      } else {
        return paymentWayInfoBuilder_.getCount();
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.PaymentTermInfo getPaymentWayInfo(int index) {
      if (paymentWayInfoBuilder_ == null) {
        return paymentWayInfo_.get(index);
      } else {
        return paymentWayInfoBuilder_.getMessage(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder setPaymentWayInfo(
        int index, com.kikitrade.kcustomer.facade.PaymentTermInfo value) {
      if (paymentWayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.set(index, value);
        onChanged();
      } else {
        paymentWayInfoBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder setPaymentWayInfo(
        int index, com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder builderForValue) {
      if (paymentWayInfoBuilder_ == null) {
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.set(index, builderForValue.build());
        onChanged();
      } else {
        paymentWayInfoBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder addPaymentWayInfo(com.kikitrade.kcustomer.facade.PaymentTermInfo value) {
      if (paymentWayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.add(value);
        onChanged();
      } else {
        paymentWayInfoBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder addPaymentWayInfo(
        int index, com.kikitrade.kcustomer.facade.PaymentTermInfo value) {
      if (paymentWayInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.add(index, value);
        onChanged();
      } else {
        paymentWayInfoBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder addPaymentWayInfo(
        com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder builderForValue) {
      if (paymentWayInfoBuilder_ == null) {
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.add(builderForValue.build());
        onChanged();
      } else {
        paymentWayInfoBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder addPaymentWayInfo(
        int index, com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder builderForValue) {
      if (paymentWayInfoBuilder_ == null) {
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.add(index, builderForValue.build());
        onChanged();
      } else {
        paymentWayInfoBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder addAllPaymentWayInfo(
        java.lang.Iterable<? extends com.kikitrade.kcustomer.facade.PaymentTermInfo> values) {
      if (paymentWayInfoBuilder_ == null) {
        ensurePaymentWayInfoIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, paymentWayInfo_);
        onChanged();
      } else {
        paymentWayInfoBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder clearPaymentWayInfo() {
      if (paymentWayInfoBuilder_ == null) {
        paymentWayInfo_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
      } else {
        paymentWayInfoBuilder_.clear();
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public Builder removePaymentWayInfo(int index) {
      if (paymentWayInfoBuilder_ == null) {
        ensurePaymentWayInfoIsMutable();
        paymentWayInfo_.remove(index);
        onChanged();
      } else {
        paymentWayInfoBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder getPaymentWayInfoBuilder(
        int index) {
      return getPaymentWayInfoFieldBuilder().getBuilder(index);
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder getPaymentWayInfoOrBuilder(
        int index) {
      if (paymentWayInfoBuilder_ == null) {
        return paymentWayInfo_.get(index);  } else {
        return paymentWayInfoBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public java.util.List<? extends com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder> 
         getPaymentWayInfoOrBuilderList() {
      if (paymentWayInfoBuilder_ != null) {
        return paymentWayInfoBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(paymentWayInfo_);
      }
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder addPaymentWayInfoBuilder() {
      return getPaymentWayInfoFieldBuilder().addBuilder(
          com.kikitrade.kcustomer.facade.PaymentTermInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder addPaymentWayInfoBuilder(
        int index) {
      return getPaymentWayInfoFieldBuilder().addBuilder(
          index, com.kikitrade.kcustomer.facade.PaymentTermInfo.getDefaultInstance());
    }
    /**
     * <code>repeated .com.kikitrade.kcustomer.facade.PaymentTermInfo paymentWayInfo = 3;</code>
     */
    public java.util.List<com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder> 
         getPaymentWayInfoBuilderList() {
      return getPaymentWayInfoFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilderV3<
        com.kikitrade.kcustomer.facade.PaymentTermInfo, com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder, com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder> 
        getPaymentWayInfoFieldBuilder() {
      if (paymentWayInfoBuilder_ == null) {
        paymentWayInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
            com.kikitrade.kcustomer.facade.PaymentTermInfo, com.kikitrade.kcustomer.facade.PaymentTermInfo.Builder, com.kikitrade.kcustomer.facade.PaymentTermInfoOrBuilder>(
                paymentWayInfo_,
                ((bitField0_ & 0x00000001) != 0),
                getParentForChildren(),
                isClean());
        paymentWayInfo_ = null;
      }
      return paymentWayInfoBuilder_;
    }
    @java.lang.Override
    public final Builder setUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.setUnknownFields(unknownFields);
    }

    @java.lang.Override
    public final Builder mergeUnknownFields(
        final com.google.protobuf.UnknownFieldSet unknownFields) {
      return super.mergeUnknownFields(unknownFields);
    }


    // @@protoc_insertion_point(builder_scope:com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse)
  }

  // @@protoc_insertion_point(class_scope:com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse)
  private static final com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse();
  }

  public static com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<QueryPaymentTermListResponse>
      PARSER = new com.google.protobuf.AbstractParser<QueryPaymentTermListResponse>() {
    @java.lang.Override
    public QueryPaymentTermListResponse parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return new QueryPaymentTermListResponse(input, extensionRegistry);
    }
  };

  public static com.google.protobuf.Parser<QueryPaymentTermListResponse> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<QueryPaymentTermListResponse> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.kikitrade.kcustomer.facade.QueryPaymentTermListResponse getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

