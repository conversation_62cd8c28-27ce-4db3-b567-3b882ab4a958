// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerKycFacade.proto

package com.kikitrade.kcustomer.facade.kyc;

/**
 * Protobuf enum {@code com.kikitrade.kcustomer.facade.kyc.KycStatusEnum}
 */
public enum KycStatusEnum
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   *(0, "未认证"),
   * </pre>
   *
   * <code>UNVERIFIED = 0;</code>
   */
  UNVERIFIED(0),
  /**
   * <pre>
   *(1, "L1审核中"),
   * </pre>
   *
   * <code>L1_AUDITING = 1;</code>
   */
  L1_AUDITING(1),
  /**
   * <pre>
   *(2, "L1已拒绝"),
   * </pre>
   *
   * <code>L1_REJECT = 2;</code>
   */
  L1_REJECT(2),
  /**
   * <pre>
   *(3, "L1已通过");
   * </pre>
   *
   * <code>L1_SUCCESS = 3;</code>
   */
  L1_SUCCESS(3),
  /**
   * <pre>
   *(4, "L2审核中"),
   * </pre>
   *
   * <code>L2_AUDITING = 4;</code>
   */
  L2_AUDITING(4),
  /**
   * <pre>
   *(5, "L2已拒绝"),
   * </pre>
   *
   * <code>L2_REJECT = 5;</code>
   */
  L2_REJECT(5),
  /**
   * <pre>
   *(6, "L2已通过");
   * </pre>
   *
   * <code>L2_SUCCESS = 6;</code>
   */
  L2_SUCCESS(6),
  UNRECOGNIZED(-1),
  ;

  /**
   * <pre>
   *(0, "未认证"),
   * </pre>
   *
   * <code>UNVERIFIED = 0;</code>
   */
  public static final int UNVERIFIED_VALUE = 0;
  /**
   * <pre>
   *(1, "L1审核中"),
   * </pre>
   *
   * <code>L1_AUDITING = 1;</code>
   */
  public static final int L1_AUDITING_VALUE = 1;
  /**
   * <pre>
   *(2, "L1已拒绝"),
   * </pre>
   *
   * <code>L1_REJECT = 2;</code>
   */
  public static final int L1_REJECT_VALUE = 2;
  /**
   * <pre>
   *(3, "L1已通过");
   * </pre>
   *
   * <code>L1_SUCCESS = 3;</code>
   */
  public static final int L1_SUCCESS_VALUE = 3;
  /**
   * <pre>
   *(4, "L2审核中"),
   * </pre>
   *
   * <code>L2_AUDITING = 4;</code>
   */
  public static final int L2_AUDITING_VALUE = 4;
  /**
   * <pre>
   *(5, "L2已拒绝"),
   * </pre>
   *
   * <code>L2_REJECT = 5;</code>
   */
  public static final int L2_REJECT_VALUE = 5;
  /**
   * <pre>
   *(6, "L2已通过");
   * </pre>
   *
   * <code>L2_SUCCESS = 6;</code>
   */
  public static final int L2_SUCCESS_VALUE = 6;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static KycStatusEnum valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static KycStatusEnum forNumber(int value) {
    switch (value) {
      case 0: return UNVERIFIED;
      case 1: return L1_AUDITING;
      case 2: return L1_REJECT;
      case 3: return L1_SUCCESS;
      case 4: return L2_AUDITING;
      case 5: return L2_REJECT;
      case 6: return L2_SUCCESS;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<KycStatusEnum>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      KycStatusEnum> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<KycStatusEnum>() {
          public KycStatusEnum findValueByNumber(int number) {
            return KycStatusEnum.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.kikitrade.kcustomer.facade.kyc.CustomerKycFacadeOuterClass.getDescriptor().getEnumTypes().get(0);
  }

  private static final KycStatusEnum[] VALUES = values();

  public static KycStatusEnum valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private KycStatusEnum(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:com.kikitrade.kcustomer.facade.kyc.KycStatusEnum)
}

