// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: CustomerKyc1Facade.proto

package com.kikitrade.kcustomer.facade.kyc1;

public interface AuditKycResponseOrBuilder extends
    // @@protoc_insertion_point(interface_extends:com.kikitrade.kcustomer.facade.kyc1.AuditKycResponse)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>bool success = 1;</code>
   * @return The success.
   */
  boolean getSuccess();

  /**
   * <code>string message = 2;</code>
   * @return The message.
   */
  java.lang.String getMessage();
  /**
   * <code>string message = 2;</code>
   * @return The bytes for message.
   */
  com.google.protobuf.ByteString
      getMessageBytes();

  /**
   * <code>.com.kikitrade.kcustomer.facade.kyc1.CustomerKyc customer = 3;</code>
   * @return Whether the customer field is set.
   */
  boolean hasCustomer();
  /**
   * <code>.com.kikitrade.kcustomer.facade.kyc1.CustomerKyc customer = 3;</code>
   * @return The customer.
   */
  com.kikitrade.kcustomer.facade.kyc1.CustomerKyc getCustomer();
  /**
   * <code>.com.kikitrade.kcustomer.facade.kyc1.CustomerKyc customer = 3;</code>
   */
  com.kikitrade.kcustomer.facade.kyc1.CustomerKycOrBuilder getCustomerOrBuilder();
}
