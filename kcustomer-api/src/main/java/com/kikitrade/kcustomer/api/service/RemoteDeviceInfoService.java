package com.kikitrade.kcustomer.api.service;

import com.kikitrade.kcustomer.api.model.DeviceInfoDTO;

/**
 * 设备信息服务
 */
public interface RemoteDeviceInfoService {

    boolean add(DeviceInfoDTO deviceInfo);

    /**
     * 获取用户最近一次操作的设备信息
     * @param saasId saasId
     * @param customerId customerId
     * @return 设备信息
     */
    DeviceInfoDTO getLastByCustomerId(String saasId, String customerId);
    /**
     * 根据设备ID获取设备信息
     * @param saasId saasId
     * @param deviceId deviceId
     * @return 设备信息
     */
    DeviceInfoDTO getByDeviceId(String saasId, String deviceId);

}
