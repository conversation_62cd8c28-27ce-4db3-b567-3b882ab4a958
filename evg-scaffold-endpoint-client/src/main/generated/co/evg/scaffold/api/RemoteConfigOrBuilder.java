// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemoteEndPointService.proto

package co.evg.scaffold.api;

public interface RemoteConfigOrBuilder extends
    // @@protoc_insertion_point(interface_extends:co.evg.scaffold.api.RemoteConfig)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string url = 1;</code>
   * @return The url.
   */
  java.lang.String getUrl();
  /**
   * <code>string url = 1;</code>
   * @return The bytes for url.
   */
  com.google.protobuf.ByteString
      getUrlBytes();

  /**
   * <code>string priceUrl = 2;</code>
   * @return The priceUrl.
   */
  java.lang.String getPriceUrl();
  /**
   * <code>string priceUrl = 2;</code>
   * @return The bytes for priceUrl.
   */
  com.google.protobuf.ByteString
      getPriceUrlBytes();

  /**
   * <code>string cdnUrl = 3;</code>
   * @return The cdnUrl.
   */
  java.lang.String getCdnUrl();
  /**
   * <code>string cdnUrl = 3;</code>
   * @return The bytes for cdnUrl.
   */
  com.google.protobuf.ByteString
      getCdnUrlBytes();
}
