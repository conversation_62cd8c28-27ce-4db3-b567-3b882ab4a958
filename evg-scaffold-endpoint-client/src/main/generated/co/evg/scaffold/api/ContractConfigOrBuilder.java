// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: RemoteEndPointService.proto

package co.evg.scaffold.api;

public interface ContractConfigOrBuilder extends
    // @@protoc_insertion_point(interface_extends:co.evg.scaffold.api.ContractConfig)
    com.google.protobuf.MessageOrBuilder {

  /**
   * <code>string address = 1;</code>
   * @return The address.
   */
  java.lang.String getAddress();
  /**
   * <code>string address = 1;</code>
   * @return The bytes for address.
   */
  com.google.protobuf.ByteString
      getAddressBytes();
}
