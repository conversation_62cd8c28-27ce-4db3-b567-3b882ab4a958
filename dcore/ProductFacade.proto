syntax = "proto3";
import "google/protobuf/timestamp.proto";
package com.kikitrade.dcore.service.facade.product;
option java_package = "com.kikitrade.dcore.service.facade.product";
option java_multiple_files = true;

// 产品规格参数定义
/*
  *********产品规格参数定义********
  会员产品：
  {"text":"day","key":"day"}

 */

//////////////////////以下服务定义

enum CHANNEL {
  shopify=0;
}

enum ProductType {
  MEDIA = 0;
  EVENT = 1;
  MEMBERSHIP = 2;

}

enum Currency {
  USD = 0;
}

enum Status {
  Active = 0;
  Disable = 1;
}

message UpsertProductRequest{
    string saasId = 1;
    Spu spu = 2;
}

message SpecAttr{
  string key = 1;
  string text = 2;
}

message Spu{
  string productId = 1;
  string sourceId = 2;    //业务id，会员卡类型，这里为会员等级，其他为业务唯一id标识
  string channelProductId = 3;
  ProductType productType = 4;  // 商品类型
  string customerId =5;     //明星用户id
  CHANNEL channel=6;     //售卖的渠道
  repeated SpecAttr specAttrs = 7;  //会员产品规格参数见页面最上方
  Status status = 8;    //状态
}

message UpsertProductResponse{
  bool success = 1;
  string message = 2;
  string productId = 3;
}



message Sku{
    string id = 1;      //修改时的商品id
    string skuMedia = 2;  //商品主图
    string name = 3;     //sku名称
    map<string, string> specParam = 4;   //规格参数：保存会员等级价格包时，天数信息在specParam中。key取本页头部定义的规格参数中设置的key
    double price = 5;    //价格
    Currency currency = 6;  //币种信息
    string channelSkuId=7;    //渠道商品id
    string productId = 8;
    Status status = 9;    //状态
    string description = 10; //描述
}

message UpsertSkuRequest{
  string saasId = 1;
  Sku sku = 2;
}


message UpsertSkuResponse{
  bool success = 1;
  string message = 2;
  string skuId = 3;
}


message DelSkuRequest{
  string saasId = 1;
  repeated string skuIds = 2;
}


message CommonResponse{
  bool success = 1;
  string message = 2;
}


service ProductFacade{

  /**
    新增/保存 产品信息
   */
  rpc upsertProduct(UpsertProductRequest) returns (UpsertProductResponse);

  /**
    新增/保存 sku
   */
  rpc upsertSku(UpsertSkuRequest) returns (UpsertSkuResponse);

  /**
    根据id删除sku
   */
  rpc delSkus(DelSkuRequest) returns (CommonResponse);


}