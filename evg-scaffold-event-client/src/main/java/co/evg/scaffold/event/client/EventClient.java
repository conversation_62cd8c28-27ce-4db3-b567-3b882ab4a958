package co.evg.scaffold.event.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.SendResult;
import com.kikitrade.framework.ons.OnsProducer;
import co.evg.scaffold.event.client.autoconfigure.EventProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class EventClient {
    private OnsProducer onsProducer;
    private EventProperties eventProperties;
    private String defaultSource;

    public EventClient() {
    }

    /**
     * 异步发送事件，不支持自定义消息tag
     *
     * @param event
     */
    public void asyncPush(EventDTO event) {
        try {
            log.info("event_push :{}", JSONObject.toJSONString(event));
            String message = buildMessage(event);
            if (StringUtils.isNotBlank(message)) {
                onsProducer.asyncSend(eventProperties.getTopic(), message);
            }
        } catch (Exception e) {
            log.error("event_push error", e);
        }
    }


    /**
     * 同步发送事件
     *
     * @param event
     * @return
     */
    public boolean push(EventDTO event) {
        try {
            log.info("event_push :{}", JSONObject.toJSONString(event));
            String message = buildMessage(event);
            if (StringUtils.isNotBlank(message)) {
                SendResult result;
                if (StringUtils.isNotBlank(event.getOnsTag())) {
                    result = onsProducer.send(eventProperties.getTopic(), event.getOnsTag(), message);
                } else {
                    result = onsProducer.send(eventProperties.getTopic(), message);
                }
                log.info("event_push :{}, messageId:{}", JSONObject.toJSONString(event), result.getMessageId());
                return null != result.getMessageId();
            }
        } catch (Exception e) {
            log.error("event_push error", e);
        }
        return false;
    }

    public boolean verifyDeviceToken(String deviceToken) {
        String aliyunRiskTokenEventCode = eventProperties.getAliyunRiskTokenEventCode();
        log.info("checkDeviceToken start, deviceToken {}, aliyunRiskTokenEventCode {}", deviceToken, aliyunRiskTokenEventCode);
        if (StringUtils.isBlank(aliyunRiskTokenEventCode)) {
            return true;
        }
        try {
            SHPLClient instance = SHPLClient.getInstance(eventProperties.getAliyunRiskDomain(),
                    eventProperties.getAliyunRiskRegion(),
                    eventProperties.getAliyunRiskAk(),
                    eventProperties.getAliyunRiskSk());
            JSONObject body = new JSONObject();
            body.put("deviceToken", deviceToken);
            body.put("DEenv", eventProperties.getEnv());
            body.put("DEtimestamp", System.currentTimeMillis());
            SHPLClient.SHPLResponse res = instance.getSHPLResponse(aliyunRiskTokenEventCode, body.toJSONString());
            log.info("checkDeviceToken finish, body={}, res={}", body, JSON.toJSONString(res));
            int code = res.getCode();
            if (200 != code) {
                log.warn("checkDeviceToken not success, body={}, res={}", body, res.getData());
                return true;
            }
//            String finalDecision = (String) res.getData().get(RiskConstants.RiskReplyParam.DATA_FINAL_DECISION.key());
//            if (REJECT.name().equals(finalDecision)){
//                log.warn("checkDeviceToken verify failed, body={}, finalDecision={}, requestId={}", body, finalDecision, res.getRequestId());
//                return false;
//            }
            return true;
        } catch (Exception e) {
            log.error("checkDeviceToken error, deviceToken {}", deviceToken);
        }
        return false;
    }


    private String buildMessage(EventDTO event) {
        try {
            if (Strings.isBlank(event.getName())) {
                log.error("event.name is blank");
                return null;
            }
            if (null == event.getTime()) {
                log.error("event.time is null");
                return null;
            }
            if (Strings.isBlank(event.getCustomerId())) {
                log.error("event.customerId is null");
                return null;
            }
            String rgex2 = "^[0-9a-zA-Z_-]{1,}$";
            Pattern pattern2 = Pattern.compile(rgex2);
            Matcher m2 = pattern2.matcher(event.getName());
            {
            }
            while (!m2.find()) {
                log.error("event.name Contains only letters, digits, and underscores (_)");
                return null;
            }
            if (Strings.isBlank(event.getSource())) {
                event.setSource(getDefaultSource());
            }
            event.setName(event.getName().toLowerCase());
            JSONObject eventJSON = (JSONObject) JSONObject.toJSON(event);
            return eventJSON.toJSONString();
        } catch (Exception e) {
            log.error("buildMessage error ", e);
        }
        return null;
    }

    public void setDefaultSource(String defaultSource) {
        this.defaultSource = defaultSource;
    }

    public String getDefaultSource() {
        return defaultSource;
    }

    public void setOnsProducer(OnsProducer onsProducer) {
        this.onsProducer = onsProducer;
    }

    public void setEventProperties(EventProperties eventProperties) {
        this.eventProperties = eventProperties;
    }
}
