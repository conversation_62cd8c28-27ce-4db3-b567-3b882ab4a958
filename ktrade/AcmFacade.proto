syntax = "proto3";
option java_package = "com.kikitrade.ktrade.facade.acm";
option java_multiple_files = true;

//message AcmPublishRequest {
//  enum Action {
//    delete = 0;
//    update = 1;
//  }
//  Action action = 1;
//  repeated Entry entries = 2;
//}
//
//message AcmPublishReply {
//  bool success = 1;
//  string msg = 2;
//}
//
message Entry {
  string key = 1;
  string val = 2;
}

message AcmQueryRequest {
  repeated string keys = 1;
}

message AcmQueryReply {
  bool success = 1;
  string msg = 2;
  repeated Entry entries = 3;
}

service AcmFacade {
//  rpc publish(AcmPublishRequest) returns (AcmPublishReply);
  rpc query(AcmQueryRequest) returns (AcmQueryReply);
}
